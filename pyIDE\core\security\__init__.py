"""
Security and Validation Module

This module provides security features including:
- Ignore pattern handling (.pyideignore files)
- Path validation and workspace boundary enforcement
- File access control and validation
"""

from .ignore import IgnoreController
from .validator import PathValidator, SecurityValidator

__all__ = [
    "IgnoreController",
    "PathValidator", 
    "SecurityValidator"
]
