# PyIDE Implementation Summary

## Overview

PyIDE is a complete Python-based IDE implementation that replicates the AI interaction and file operations workflow from Cline. It demonstrates the exact technical implementation of two key features:

1. **Response Parsing/Code Extraction**: XML-based tool call extraction from AI responses
2. **File Operations Integration**: Secure file operations with user approval and validation

## Architecture

### Core Components

```
pyIDE/
├── main.py                 # Main application orchestrator
├── simple_start.py         # Minimal startup script for testing
├── run.py                  # Production startup with dependency checks
├── config.json            # Configuration with OpenRouter API integration
├── requirements.txt       # Python dependencies
├── .pyideignore           # Security ignore patterns
│
├── core/                  # Core functionality
│   ├── ai_client.py       # OpenRouter API client with streaming
│   ├── parser.py          # XML response parser (state machine)
│   │
│   ├── security/          # Security and validation
│   │   ├── ignore.py      # .pyideignore pattern matching
│   │   └── validator.py   # Path validation and security checks
│   │
│   ├── tools/             # Tool implementations
│   │   ├── file_ops.py    # File operations (read, write, replace, list)
│   │   ├── command.py     # Command execution
│   │   └── search.py      # File search with regex
│   │
│   └── ui/                # User interface
│       ├── cli.py         # Rich CLI with syntax highlighting
│       └── diff_view.py   # Diff viewer for file changes
│
├── examples/              # Usage examples
│   └── demo.py           # Programmatic usage demonstration
│
└── tests/                 # Test suite
    └── test_parser.py     # Parser tests
```

## Technical Implementation Details

### 1. Response Parsing/Code Extraction

**Location**: `core/parser.py`

**Algorithm**: Character-by-character state machine parser that replicates Cline's `parseAssistantMessageV2`

**Key Features**:
- **Three-state parsing**: TEXT → TOOL_USE → PARAMETER
- **Streaming support**: Handles partial responses in real-time
- **Nested content handling**: Special logic for `<content>` parameters
- **XML format**: `<tool_name><parameter>value</parameter></tool_name>`

**Example XML Format**:
```xml
<write_to_file>
<path>hello.py</path>
<content>print("Hello, World!")</content>
</write_to_file>
```

**Parsing Process**:
1. **Character-by-character scanning** for tool opening tags
2. **State transitions** based on tag detection
3. **Parameter extraction** with nested content support
4. **Content block generation** (TextContent vs ToolUse)

### 2. File Operations Integration

**Location**: `core/tools/file_ops.py`

**Security Model**:
- **Ignore patterns**: `.pyideignore` file with gitignore syntax
- **Path validation**: Workspace boundary enforcement
- **User approval**: Interactive approval for all operations
- **Size limits**: 20MB file size limit
- **Encoding detection**: Automatic character encoding detection

**File Operation Workflow**:
1. **Security validation** → Path safety checks
2. **Ignore pattern check** → `.pyideignore` validation
3. **User approval** → Interactive diff preview
4. **Atomic operation** → File system modification
5. **Result feedback** → Success/error reporting

**Supported Operations**:
- `read_file`: Read with encoding detection
- `write_to_file`: Create/overwrite with approval
- `replace_in_file`: SEARCH/REPLACE block editing
- `list_files`: Directory listing with filtering
- `search_files`: Regex-based file search

## API Integration

### OpenRouter API Configuration

**Model**: `deepseek/deepseek-r1-0528:free`
**API Key**: `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`

**Features**:
- **Streaming responses**: Real-time AI output
- **Retry logic**: Automatic retry with exponential backoff
- **Error handling**: Comprehensive error recovery
- **Token tracking**: Usage monitoring and cost tracking

### System Prompt Format

The system prompt defines the exact XML format for tool calls:

```
Tool use is formatted using XML-style tags:

<tool_name>
<parameter1_name>value1</parameter1_name>
<parameter2_name>value2</parameter2_name>
</tool_name>
```

## Security Features

### Multi-Layer Security

1. **Ignore Pattern Matching**:
   - Uses `.pyideignore` file (gitignore syntax)
   - Blocks access to sensitive files/directories
   - Supports wildcards and path patterns

2. **Workspace Boundaries**:
   - Restricts operations to workspace directory
   - Validates all file paths
   - Prevents directory traversal attacks

3. **User Approval System**:
   - Interactive approval for file operations
   - Diff preview before changes
   - Rollback capability on denial

4. **Command Validation**:
   - Filters dangerous commands
   - Validates shell metacharacters
   - Blocks system-level operations

## Usage Examples

### Quick Start

```bash
# Install minimal dependencies
pip install aiohttp rich

# Run simple demo
python simple_start.py

# Or install full dependencies
pip install -r requirements.txt

# Run full IDE
python main.py
```

### Programmatic Usage

```python
from core.ai_client import AIClient
from core.parser import ResponseParser

# AI interaction
async with AIClient(config) as client:
    async for chunk in client.stream_request("Create a Python script"):
        if chunk.type == "text":
            print(chunk.content, end="")

# Response parsing
parser = ResponseParser()
content_blocks = parser.parse_complete(ai_response)
tool_calls = parser.extract_tool_calls(content_blocks)
```

### Interactive Usage

```
PyIDE> Create a hello world Python script
