#!/usr/bin/env python3
"""
Simple PyIDE Startup Script

This script starts PyIDE with minimal dependencies for testing.
It only requires the core Python standard library and basic packages.
"""

import sys
import os
import asyncio
import json
from pathlib import Path

# Add the current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def check_minimal_dependencies():
    """Check if minimal required dependencies are installed"""
    required_packages = [
        'aiohttp',
        'rich'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\nPlease install minimal dependencies:")
        print("  pip install aiohttp rich")
        return False
    
    return True

def create_minimal_config():
    """Create a minimal configuration if none exists"""
    config_file = current_dir / "config.json"
    
    if not config_file.exists():
        minimal_config = {
            "api": {
                "provider": "openrouter",
                "key": "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",
                "model": "deepseek/deepseek-r1-0528:free",
                "base_url": "https://openrouter.ai/api/v1",
                "timeout": 60,
                "max_retries": 3
            },
            "security": {
                "max_file_size": 20971520,
                "workspace_only": True,
                "require_approval": True,
                "ignore_file": ".pyideignore"
            },
            "ui": {
                "interface": "cli",
                "show_diffs": True,
                "auto_approve": False,
                "colors": True
            },
            "workspace": {
                "default_directory": "."
            },
            "logging": {
                "level": "INFO"
            }
        }
        
        with open(config_file, 'w') as f:
            json.dump(minimal_config, f, indent=2)
        
        print(f"✅ Created minimal config: {config_file}")
    
    return True

async def simple_demo():
    """Run a simple demo without full PyIDE"""
    print("🤖 PyIDE Simple Demo")
    print("=" * 50)
    
    try:
        # Import core components
        from core.ai_client import AIClient
        from core.parser import ResponseParser
        
        # Load config
        config_file = current_dir / "config.json"
        with open(config_file) as f:
            config = json.load(f)
        
        print("✅ Configuration loaded")
        
        # Test AI client
        print("\n🔗 Testing AI connection...")
        async with AIClient(config) as ai_client:
            response = await ai_client.simple_request(
                "Say hello and explain what you are in one sentence.",
                str(current_dir)
            )
            print(f"AI Response: {response}")
        
        print("\n🔍 Testing response parser...")
        parser = ResponseParser()
        
        # Test parsing
        sample_response = """I'll create a test file for you.

<write_to_file>
<path>test.py</path>
<content>print("Hello from PyIDE!")</content>
</write_to_file>

File created successfully!"""
        
        content_blocks = parser.parse_complete(sample_response)
        tool_calls = parser.extract_tool_calls(content_blocks)
        
        print(f"Parsed {len(content_blocks)} content blocks")
        print(f"Found {len(tool_calls)} tool calls")
        
        if tool_calls:
            tool = tool_calls[0]
            print(f"Tool: {tool.name}")
            print(f"Parameters: {list(tool.params.keys())}")
        
        print("\n✅ Simple demo completed successfully!")
        print("\nTo run the full PyIDE:")
        print("  1. Install all dependencies: pip install -r requirements.txt")
        print("  2. Run: python main.py")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()

async def interactive_chat():
    """Simple interactive chat without full UI"""
    print("\n💬 Simple Interactive Chat")
    print("Type 'exit' to quit")
    print("-" * 30)
    
    try:
        from core.ai_client import AIClient
        
        config_file = current_dir / "config.json"
        with open(config_file) as f:
            config = json.load(f)
        
        async with AIClient(config) as ai_client:
            while True:
                try:
                    user_input = input("\nYou: ").strip()
                    
                    if user_input.lower() in ['exit', 'quit', 'bye']:
                        print("Goodbye!")
                        break
                    
                    if not user_input:
                        continue
                    
                    print("AI: ", end="")
                    async for chunk in ai_client.stream_request(user_input, str(current_dir)):
                        if chunk.type == "text":
                            print(chunk.content, end="")
                        elif chunk.type == "error":
                            print(f"\nError: {chunk.error}")
                            break
                    print()  # New line after response
                    
                except KeyboardInterrupt:
                    print("\nUse 'exit' to quit")
                    continue
                except Exception as e:
                    print(f"\nError: {e}")
                    continue
                    
    except Exception as e:
        print(f"❌ Chat failed: {e}")

async def main():
    """Main function"""
    print("🚀 PyIDE Simple Startup")
    print("=" * 50)
    
    # Check minimal dependencies
    if not check_minimal_dependencies():
        sys.exit(1)
    
    # Create minimal config
    create_minimal_config()
    
    print("\nChoose an option:")
    print("1. Run simple demo")
    print("2. Interactive chat")
    print("3. Exit")
    
    try:
        choice = input("\nEnter choice (1-3): ").strip()
        
        if choice == "1":
            await simple_demo()
        elif choice == "2":
            await interactive_chat()
        elif choice == "3":
            print("Goodbye!")
        else:
            print("Invalid choice")
            
    except KeyboardInterrupt:
        print("\nGoodbye!")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
