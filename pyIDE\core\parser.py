"""
Response Parser for XML-based Tool Call Extraction

This module implements the character-by-character state machine parser that:
- Extracts tool calls from AI responses
- <PERSON>les mixed text and tool use content
- Supports partial/streaming content blocks
- Provides special handling for nested content
"""

import re
import logging
from typing import List, Dict, Optional, Union
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class ParseState(Enum):
    """Parser state enumeration"""
    TEXT = "text"
    TOOL_USE = "tool_use"
    PARAMETER = "parameter"


@dataclass
class ContentBlock:
    """Base class for content blocks"""
    type: str
    partial: bool = False


@dataclass
class TextContent(ContentBlock):
    """Text content block"""
    content: str = ""
    type: str = "text"
    partial: bool = False


@dataclass
class ToolUse(ContentBlock):
    """Tool use content block"""
    name: str = ""
    params: Dict[str, str] = None
    type: str = "tool_use"
    partial: bool = False

    def __post_init__(self):
        if self.params is None:
            self.params = {}


class ResponseParser:
    """
    XML-based response parser that extracts tool calls from AI responses
    Implements the same parsing logic as Cline's parseAssistantMessageV2
    """
    
    # Supported tool names (must match system prompt)
    TOOL_NAMES = [
        "read_file",
        "write_to_file", 
        "replace_in_file",
        "list_files",
        "search_files",
        "execute_command",
        "ask_followup_question"
    ]
    
    # Parameter names that can appear in tools
    PARAM_NAMES = [
        "path",
        "content", 
        "diff",
        "recursive",
        "regex",
        "file_pattern",
        "command",
        "question"
    ]
    
    def __init__(self):
        self.reset()
        
    def reset(self):
        """Reset parser state"""
        self.content_blocks: List[ContentBlock] = []
        self.current_text_start = 0
        self.current_text_content: Optional[TextContent] = None
        self.current_tool_use: Optional[ToolUse] = None
        self.current_param_name: Optional[str] = None
        self.current_param_start = 0
        
    def parse_complete(self, message: str) -> List[ContentBlock]:
        """Parse a complete message and return content blocks"""
        self.reset()
        return self._parse_message(message, complete=True)
        
    def parse_streaming(self, message: str) -> List[ContentBlock]:
        """Parse a streaming (potentially partial) message"""
        return self._parse_message(message, complete=False)
        
    def _parse_message(self, message: str, complete: bool = True) -> List[ContentBlock]:
        """
        Core parsing logic - character by character state machine
        Replicates Cline's parseAssistantMessageV2 algorithm
        """
        content_blocks: List[ContentBlock] = []
        current_text_start = 0
        current_text_content: Optional[TextContent] = None
        current_tool_use: Optional[ToolUse] = None
        current_param_name: Optional[str] = None
        current_param_start = 0
        
        # Pre-compute tag maps for efficiency
        tool_open_tags = {f"<{tool}>": tool for tool in self.TOOL_NAMES}
        tool_close_tags = {f"</{tool}>": tool for tool in self.TOOL_NAMES}
        param_open_tags = {f"<{param}>": param for param in self.PARAM_NAMES}
        param_close_tags = {f"</{param}>": param for param in self.PARAM_NAMES}
        
        i = 0
        while i < len(message):
            char = message[i]
            
            # State: Parsing a tool parameter
            if current_tool_use and current_param_name:
                param_close_tag = f"</{current_param_name}>"
                
                # Check if we've reached the end of the parameter
                if message[i:].startswith(param_close_tag):
                    # Extract parameter value
                    param_value = message[current_param_start:i]
                    
                    # Special handling for content parameter in write_to_file/replace_in_file
                    if (current_param_name == "content" and 
                        current_tool_use.name in ["write_to_file", "replace_in_file"]):
                        # Use lastIndexOf logic to handle nested closing tags
                        param_value = self._extract_content_parameter(
                            message, current_param_start, i, param_close_tag
                        )
                    
                    current_tool_use.params[current_param_name] = param_value.strip()
                    current_param_name = None
                    i += len(param_close_tag) - 1
                    
            # State: Parsing a tool use (but not a specific parameter)
            elif current_tool_use:
                tool_close_tag = f"</{current_tool_use.name}>"
                
                # Check if we've reached the end of the tool use
                if message[i:].startswith(tool_close_tag):
                    current_tool_use.partial = False
                    content_blocks.append(current_tool_use)
                    current_tool_use = None
                    current_text_start = i + len(tool_close_tag)
                    i += len(tool_close_tag) - 1
                else:
                    # Check if starting a new parameter
                    for tag, param_name in param_open_tags.items():
                        if message[i:].startswith(tag):
                            current_param_name = param_name
                            current_param_start = i + len(tag)
                            i += len(tag) - 1
                            break
                            
            # State: Parsing text or looking for tool start
            else:
                # Check if starting a new tool use
                tool_started = False
                for tag, tool_name in tool_open_tags.items():
                    if message[i:].startswith(tag):
                        # End current text block if one exists
                        if current_text_content or i > current_text_start:
                            text_content = message[current_text_start:i].strip()
                            if text_content:
                                content_blocks.append(TextContent(
                                    content=text_content,
                                    partial=False
                                ))
                        
                        # Start new tool use
                        current_tool_use = ToolUse(
                            name=tool_name,
                            params={},
                            partial=True
                        )
                        i += len(tag) - 1
                        tool_started = True
                        break
                        
                if not tool_started:
                    # Continue accumulating text
                    if not current_text_content:
                        current_text_content = TextContent(content="", partial=True)
                        
            i += 1
            
        # Handle any remaining content
        if current_tool_use:
            # Tool use was not completed
            current_tool_use.partial = not complete
            content_blocks.append(current_tool_use)
        elif current_text_content or len(message) > current_text_start:
            # Remaining text content
            text_content = message[current_text_start:].strip()
            if text_content:
                content_blocks.append(TextContent(
                    content=text_content,
                    partial=not complete
                ))
                
        return content_blocks
        
    def _extract_content_parameter(self, message: str, start: int, end: int, close_tag: str) -> str:
        """
        Extract content parameter with special handling for nested tags
        Uses lastIndexOf logic to handle cases where </content> might appear within content
        """
        content_section = message[start:end]
        
        # Find the last occurrence of the closing tag
        last_close_index = content_section.rfind(close_tag)
        
        if last_close_index != -1:
            # Use content up to the last closing tag
            return content_section[:last_close_index]
        else:
            # No closing tag found, return all content
            return content_section
            
    def extract_tool_calls(self, content_blocks: List[ContentBlock]) -> List[ToolUse]:
        """Extract only tool use blocks from content blocks"""
        return [block for block in content_blocks if isinstance(block, ToolUse)]
        
    def extract_text_content(self, content_blocks: List[ContentBlock]) -> str:
        """Extract and combine all text content from content blocks"""
        text_parts = []
        for block in content_blocks:
            if isinstance(block, TextContent):
                text_parts.append(block.content)
        return "\n".join(text_parts).strip()
        
    def validate_tool_call(self, tool_use: ToolUse) -> bool:
        """Validate that a tool call has required parameters"""
        required_params = {
            "read_file": ["path"],
            "write_to_file": ["path", "content"],
            "replace_in_file": ["path", "diff"],
            "list_files": ["path"],
            "search_files": ["path", "regex"],
            "execute_command": ["command"],
            "ask_followup_question": ["question"]
        }
        
        if tool_use.name not in required_params:
            return False
            
        for param in required_params[tool_use.name]:
            if param not in tool_use.params or not tool_use.params[param].strip():
                return False
                
        return True
        
    def format_tool_result(self, tool_name: str, result: str, success: bool = True) -> str:
        """Format tool execution result for conversation history"""
        status = "SUCCESS" if success else "ERROR"
        return f"Tool: {tool_name}\nStatus: {status}\nResult:\n{result}"
