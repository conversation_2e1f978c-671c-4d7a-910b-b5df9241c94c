{"api": {"provider": "openrouter", "key": "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", "model": "deepseek/deepseek-r1-0528:free", "base_url": "https://openrouter.ai/api/v1", "timeout": 60, "max_retries": 3, "retry_delay": 1.0}, "security": {"max_file_size": 20971520, "workspace_only": true, "require_approval": true, "ignore_file": ".<PERSON><PERSON><PERSON><PERSON><PERSON>", "allowed_extensions": [".py", ".txt", ".md", ".json", ".yaml", ".yml", ".toml", ".cfg", ".ini"], "blocked_paths": ["__pycache__", ".git", ".venv", "venv", "env", "node_modules"]}, "ui": {"interface": "cli", "show_diffs": true, "auto_approve": false, "colors": true, "diff_context_lines": 3, "max_display_lines": 50}, "tools": {"file_operations": {"read_file": true, "write_to_file": true, "replace_in_file": true, "list_files": true, "search_files": true}, "system_operations": {"execute_command": true, "ask_followup_question": true}, "advanced_features": {"browser_automation": false, "git_integration": true, "syntax_highlighting": true}}, "logging": {"level": "INFO", "file": "pyide.log", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "max_size": 10485760, "backup_count": 5}, "workspace": {"default_directory": ".", "auto_detect_project": true, "project_files": ["pyproject.toml", "setup.py", "requirements.txt", "Pipfile"], "exclude_patterns": ["*.pyc", "*.pyo", "*.pyd", "__pycache__", ".pytest_cache"]}}