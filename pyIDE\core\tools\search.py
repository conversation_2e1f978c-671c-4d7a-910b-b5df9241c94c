"""
File Search Tools

This module implements file search functionality with:
- Regex-based content search
- File pattern filtering
- Multi-file search with context
- Performance optimization for large codebases
"""

import os
import re
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any, Pattern
import fnmatch

logger = logging.getLogger(__name__)


class FileSearcher:
    """
    File search functionality with regex support and filtering
    """
    
    def __init__(self, config: Dict[str, Any], ignore_controller):
        self.config = config
        self.ignore_controller = ignore_controller
        self.workspace_path = Path(config.get("workspace", {}).get("default_directory", ".")).resolve()
        self.max_results = config.get("search", {}).get("max_results", 1000)
        self.max_file_size = config.get("security", {}).get("max_file_size", 20 * 1024 * 1024)
        
    async def search_files(self, path: str, regex: str, file_pattern: str = "*", 
                          context_lines: int = 2, case_sensitive: bool = False) -> Dict[str, Any]:
        """
        Search for regex pattern in files
        """
        result = {
            "success": False,
            "matches": [],
            "error": None,
            "search_info": {
                "pattern": regex,
                "file_pattern": file_pattern,
                "files_searched": 0,
                "total_matches": 0
            }
        }
        
        try:
            # Validate search path
            abs_path = Path(path)
            if not abs_path.is_absolute():
                abs_path = self.workspace_path / path
                
            if not abs_path.exists():
                result["error"] = f"Search path not found: {path}"
                return result
                
            if not abs_path.is_dir():
                result["error"] = f"Search path is not a directory: {path}"
                return result
                
            # Check access permissions
            if not self.ignore_controller.validate_access(str(abs_path)):
                result["error"] = f"Directory access denied by ignore patterns: {path}"
                return result
                
            # Compile regex pattern
            try:
                flags = 0 if case_sensitive else re.IGNORECASE
                pattern = re.compile(regex, flags)
            except re.error as e:
                result["error"] = f"Invalid regex pattern: {e}"
                return result
                
            # Find matching files
            matching_files = self._find_matching_files(abs_path, file_pattern)
            
            matches = []
            files_searched = 0
            total_matches = 0
            
            for file_path in matching_files:
                if total_matches >= self.max_results:
                    break
                    
                try:
                    file_matches = await self._search_in_file(
                        file_path, pattern, context_lines
                    )
                    
                    if file_matches:
                        rel_path = file_path.relative_to(self.workspace_path)
                        matches.append({
                            "file": str(rel_path),
                            "absolute_path": str(file_path),
                            "matches": file_matches,
                            "match_count": len(file_matches)
                        })
                        total_matches += len(file_matches)
                        
                    files_searched += 1
                    
                except Exception as e:
                    logger.warning(f"Error searching file {file_path}: {e}")
                    continue
                    
            result.update({
                "success": True,
                "matches": matches,
                "search_info": {
                    "pattern": regex,
                    "file_pattern": file_pattern,
                    "files_searched": files_searched,
                    "total_matches": total_matches,
                    "truncated": total_matches >= self.max_results
                }
            })
            
            logger.info(f"Search completed: {total_matches} matches in {files_searched} files")
            return result
            
        except Exception as e:
            result["error"] = f"Search error: {e}"
            logger.error(f"Search error: {e}")
            
        return result
        
    def _find_matching_files(self, search_path: Path, file_pattern: str) -> List[Path]:
        """Find files matching the file pattern"""
        matching_files = []
        
        try:
            for file_path in search_path.rglob(file_pattern):
                if not file_path.is_file():
                    continue
                    
                # Check ignore patterns
                try:
                    rel_path = file_path.relative_to(self.workspace_path)
                    if not self.ignore_controller.validate_access(str(rel_path)):
                        continue
                except ValueError:
                    # File outside workspace
                    continue
                    
                # Check file size
                try:
                    if file_path.stat().st_size > self.max_file_size:
                        logger.debug(f"Skipping large file: {file_path}")
                        continue
                except OSError:
                    continue
                    
                # Check if file is likely text
                if self._is_likely_text_file(file_path):
                    matching_files.append(file_path)
                    
        except Exception as e:
            logger.error(f"Error finding matching files: {e}")
            
        return matching_files
        
    async def _search_in_file(self, file_path: Path, pattern: Pattern, context_lines: int) -> List[Dict[str, Any]]:
        """Search for pattern in a single file"""
        matches = []
        
        try:
            # Read file content
            with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                lines = f.readlines()
                
            # Search each line
            for line_num, line in enumerate(lines, 1):
                match = pattern.search(line)
                if match:
                    # Get context lines
                    start_line = max(0, line_num - context_lines - 1)
                    end_line = min(len(lines), line_num + context_lines)
                    
                    context = []
                    for i in range(start_line, end_line):
                        context.append({
                            "line_number": i + 1,
                            "content": lines[i].rstrip('\n\r'),
                            "is_match": i + 1 == line_num
                        })
                        
                    matches.append({
                        "line_number": line_num,
                        "line_content": line.rstrip('\n\r'),
                        "match_start": match.start(),
                        "match_end": match.end(),
                        "matched_text": match.group(),
                        "context": context
                    })
                    
        except UnicodeDecodeError:
            logger.debug(f"Skipping binary file: {file_path}")
        except Exception as e:
            logger.warning(f"Error searching in file {file_path}: {e}")
            
        return matches
        
    def _is_likely_text_file(self, file_path: Path) -> bool:
        """Check if file is likely a text file"""
        # Check extension
        text_extensions = {
            '.txt', '.py', '.js', '.ts', '.html', '.css', '.json', '.xml',
            '.md', '.rst', '.yaml', '.yml', '.toml', '.ini', '.cfg',
            '.sh', '.bat', '.ps1', '.sql', '.log', '.csv', '.tsv'
        }
        
        if file_path.suffix.lower() in text_extensions:
            return True
            
        # Check for common binary extensions
        binary_extensions = {
            '.exe', '.dll', '.so', '.dylib', '.bin', '.img', '.iso',
            '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.ico', '.svg',
            '.mp3', '.mp4', '.avi', '.mov', '.wav', '.pdf', '.zip',
            '.tar', '.gz', '.7z', '.rar', '.pyc', '.pyo', '.pyd'
        }
        
        if file_path.suffix.lower() in binary_extensions:
            return False
            
        # For unknown extensions, try to read first few bytes
        try:
            with open(file_path, 'rb') as f:
                sample = f.read(1024)
                
            # Check for null bytes (common in binary files)
            if b'\x00' in sample:
                return False
                
            # Try to decode as text
            try:
                sample.decode('utf-8')
                return True
            except UnicodeDecodeError:
                return False
                
        except Exception:
            return False
            
    async def find_files_by_name(self, path: str, name_pattern: str, case_sensitive: bool = False) -> Dict[str, Any]:
        """
        Find files by name pattern
        """
        result = {
            "success": False,
            "files": [],
            "error": None,
            "search_info": {
                "pattern": name_pattern,
                "files_found": 0
            }
        }
        
        try:
            abs_path = Path(path)
            if not abs_path.is_absolute():
                abs_path = self.workspace_path / path
                
            if not abs_path.exists():
                result["error"] = f"Search path not found: {path}"
                return result
                
            # Check access permissions
            if not self.ignore_controller.validate_access(str(abs_path)):
                result["error"] = f"Directory access denied by ignore patterns: {path}"
                return result
                
            files = []
            
            # Use glob pattern matching
            if case_sensitive:
                pattern = name_pattern
            else:
                pattern = name_pattern.lower()
                
            for file_path in abs_path.rglob("*"):
                if not file_path.is_file():
                    continue
                    
                # Check ignore patterns
                try:
                    rel_path = file_path.relative_to(self.workspace_path)
                    if not self.ignore_controller.validate_access(str(rel_path)):
                        continue
                except ValueError:
                    continue
                    
                # Check name match
                file_name = file_path.name if case_sensitive else file_path.name.lower()
                if fnmatch.fnmatch(file_name, pattern):
                    files.append({
                        "name": file_path.name,
                        "path": str(file_path.relative_to(self.workspace_path)),
                        "absolute_path": str(file_path),
                        "size": file_path.stat().st_size,
                        "modified": file_path.stat().st_mtime
                    })
                    
            result.update({
                "success": True,
                "files": files,
                "search_info": {
                    "pattern": name_pattern,
                    "files_found": len(files)
                }
            })
            
            logger.info(f"Found {len(files)} files matching pattern: {name_pattern}")
            return result
            
        except Exception as e:
            result["error"] = f"File search error: {e}"
            logger.error(f"File search error: {e}")
            
        return result
