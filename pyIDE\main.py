"""
PyIDE Main Application

This is the main entry point for PyIDE that orchestrates:
- Configuration loading
- Component initialization
- AI client and tool setup
- Main interaction loop
- Error handling and logging
"""

import asyncio
import json
import logging
import sys
from pathlib import Path
from typing import Dict, Any, Optional

# Core imports
from core.ai_client import <PERSON><PERSON>lient
from core.parser import <PERSON><PERSON><PERSON><PERSON>, Too<PERSON><PERSON><PERSON>, TextContent
from core.security.ignore import IgnoreController
from core.security.validator import SecurityValidator
from core.tools.file_ops import FileOperations
from core.tools.command import CommandExecutor
from core.tools.search import FileSearcher
from core.ui.cli import CLIInterface
from core.ui.diff_view import DiffViewer


class PyIDE:
    """
    Main PyIDE application class
    """
    
    def __init__(self, config_path: str = "config.json"):
        self.config_path = config_path
        self.config: Dict[str, Any] = {}
        self.workspace_path: Optional[Path] = None
        
        # Core components
        self.ai_client: Optional[AIClient] = None
        self.parser: Optional[ResponseParser] = None
        self.ignore_controller: Optional[IgnoreController] = None
        self.security_validator: Optional[SecurityValidator] = None
        
        # Tools
        self.file_ops: Optional[FileOperations] = None
        self.command_executor: Optional[CommandExecutor] = None
        self.file_searcher: Optional[FileSearcher] = None
        
        # UI
        self.cli: Optional[CLIInterface] = None
        self.diff_viewer: Optional[DiffViewer] = None
        
        # State
        self.running = False
        
    async def initialize(self):
        """Initialize all components"""
        try:
            # Load configuration
            await self._load_config()
            
            # Setup logging
            self._setup_logging()
            
            # Initialize workspace
            self._setup_workspace()
            
            # Initialize security components
            self._initialize_security()
            
            # Initialize tools
            self._initialize_tools()
            
            # Initialize UI
            self._initialize_ui()
            
            # Initialize AI client
            await self._initialize_ai_client()
            
            # Initialize parser
            self.parser = ResponseParser()
            
            logging.info("PyIDE initialized successfully")
            
        except Exception as e:
            logging.error(f"Failed to initialize PyIDE: {e}")
            raise
            
    async def _load_config(self):
        """Load configuration from file"""
        config_file = Path(self.config_path)
        
        if not config_file.exists():
            raise FileNotFoundError(f"Configuration file not found: {self.config_path}")
            
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in configuration file: {e}")
            
    def _setup_logging(self):
        """Setup logging configuration"""
        log_config = self.config.get("logging", {})
        
        logging.basicConfig(
            level=getattr(logging, log_config.get("level", "INFO")),
            format=log_config.get("format", "%(asctime)s - %(name)s - %(levelname)s - %(message)s"),
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler(log_config.get("file", "pyide.log"))
            ]
        )
        
    def _setup_workspace(self):
        """Setup workspace directory"""
        workspace_config = self.config.get("workspace", {})
        workspace_dir = workspace_config.get("default_directory", ".")
        
        self.workspace_path = Path(workspace_dir).resolve()
        
        if not self.workspace_path.exists():
            self.workspace_path.mkdir(parents=True, exist_ok=True)
            
        logging.info(f"Workspace: {self.workspace_path}")
        
    def _initialize_security(self):
        """Initialize security components"""
        ignore_file = self.config.get("security", {}).get("ignore_file", ".pyideignore")
        
        self.ignore_controller = IgnoreController(
            str(self.workspace_path),
            ignore_file
        )
        
        self.security_validator = SecurityValidator(self.config)
        
    def _initialize_tools(self):
        """Initialize tool components"""
        self.file_ops = FileOperations(
            self.config,
            self.ignore_controller,
            self.security_validator
        )
        
        self.command_executor = CommandExecutor(
            self.config,
            self.ignore_controller,
            self.security_validator
        )
        
        self.file_searcher = FileSearcher(
            self.config,
            self.ignore_controller
        )
        
    def _initialize_ui(self):
        """Initialize UI components"""
        self.cli = CLIInterface(self.config)
        self.diff_viewer = DiffViewer(self.config)
        
    async def _initialize_ai_client(self):
        """Initialize AI client"""
        self.ai_client = AIClient(self.config)
        
    async def run(self):
        """Main application loop"""
        self.running = True
        
        try:
            # Display welcome message
            self.cli.display_welcome()
            
            # Main interaction loop
            while self.running:
                try:
                    # Get user input
                    user_input = self.cli.get_user_input()
                    
                    if not user_input.strip():
                        continue
                        
                    # Handle special commands
                    if user_input.startswith('/'):
                        await self._handle_special_command(user_input)
                        continue
                        
                    # Process AI request
                    await self._process_ai_request(user_input)
                    
                except KeyboardInterrupt:
                    self.cli.display_info("Use /exit to quit")
                    continue
                except Exception as e:
                    self.cli.display_error(f"Unexpected error: {e}")
                    logging.error(f"Unexpected error in main loop: {e}")
                    continue
                    
        except Exception as e:
            self.cli.display_error(f"Fatal error: {e}")
            logging.error(f"Fatal error: {e}")
        finally:
            await self._cleanup()
            
    async def _handle_special_command(self, command: str):
        """Handle special commands like /help, /exit, etc."""
        command = command.lower().strip()
        
        if command == "/exit" or command == "/quit":
            self.cli.display_info("Goodbye!")
            self.running = False
        elif command == "/help":
            self.cli.display_help()
        elif command == "/clear":
            self.cli.clear_screen()
            if self.ai_client:
                self.ai_client.clear_history()
            self.cli.display_success("Conversation history cleared")
        elif command == "/status":
            await self._show_status()
        elif command == "/config":
            await self._show_config()
        else:
            self.cli.display_error(f"Unknown command: {command}")
            
    async def _process_ai_request(self, user_input: str):
        """Process AI request and handle tool calls"""
        try:
            self.cli.display_ai_response_start()
            
            # Stream AI response
            async with self.ai_client as client:
                full_response = ""
                
                async for chunk in client.stream_request(user_input, str(self.workspace_path)):
                    if chunk.type == "text":
                        self.cli.display_ai_text(chunk.content)
                        full_response += chunk.content
                    elif chunk.type == "error":
                        self.cli.display_error(f"AI Error: {chunk.error}")
                        return
                    elif chunk.type == "usage":
                        # Log token usage
                        usage = chunk.usage
                        logging.info(f"Token usage: {usage}")
                        
                # Parse response for tool calls
                content_blocks = self.parser.parse_complete(full_response)
                tool_calls = self.parser.extract_tool_calls(content_blocks)
                
                # Execute tool calls
                for tool_call in tool_calls:
                    if self.parser.validate_tool_call(tool_call):
                        await self._execute_tool_call(tool_call)
                    else:
                        self.cli.display_error(f"Invalid tool call: {tool_call.name}")
                        
        except Exception as e:
            self.cli.display_error(f"Error processing AI request: {e}")
            logging.error(f"Error processing AI request: {e}")
            
    async def _execute_tool_call(self, tool_call: ToolUse):
        """Execute a tool call"""
        try:
            self.cli.display_tool_call(tool_call.name, tool_call.params)
            
            result = None
            success = False
            
            # Execute based on tool name
            if tool_call.name == "read_file":
                result = await self.file_ops.read_file(tool_call.params.get("path", ""))
                success = result.get("success", False)
                
            elif tool_call.name == "write_to_file":
                result = await self.file_ops.write_file(
                    tool_call.params.get("path", ""),
                    tool_call.params.get("content", "")
                )
                success = result.get("success", False)
                
            elif tool_call.name == "replace_in_file":
                result = await self.file_ops.replace_in_file(
                    tool_call.params.get("path", ""),
                    tool_call.params.get("diff", "")
                )
                success = result.get("success", False)
                
            elif tool_call.name == "list_files":
                recursive = tool_call.params.get("recursive", "false").lower() == "true"
                result = await self.file_ops.list_files(
                    tool_call.params.get("path", "."),
                    recursive=recursive
                )
                success = result.get("success", False)
                
            elif tool_call.name == "search_files":
                result = await self.file_searcher.search_files(
                    tool_call.params.get("path", "."),
                    tool_call.params.get("regex", ""),
                    tool_call.params.get("file_pattern", "*")
                )
                success = result.get("success", False)
                
            elif tool_call.name == "execute_command":
                result = await self.command_executor.execute_command(
                    tool_call.params.get("command", "")
                )
                success = result.get("success", False)
                
            elif tool_call.name == "ask_followup_question":
                question = tool_call.params.get("question", "")
                self.cli.display_info(f"AI Question: {question}")
                answer = self.cli.get_user_input()
                result = {"success": True, "answer": answer}
                success = True
                
            else:
                result = {"success": False, "error": f"Unknown tool: {tool_call.name}"}
                success = False
                
            # Display result
            if result:
                self.cli.display_tool_result(tool_call.name, result, success)
                
                # Add tool result to conversation history
                if self.ai_client:
                    tool_result_text = self.parser.format_tool_result(
                        tool_call.name,
                        result.get("content", str(result)),
                        success
                    )
                    self.ai_client.add_message("user", tool_result_text)
                    
        except Exception as e:
            self.cli.display_error(f"Error executing tool {tool_call.name}: {e}")
            logging.error(f"Error executing tool {tool_call.name}: {e}")
            
    async def _show_status(self):
        """Show system status"""
        status_info = {
            "Workspace": {
                "status": "ok" if self.workspace_path and self.workspace_path.exists() else "error",
                "details": str(self.workspace_path) if self.workspace_path else "Not set"
            },
            "AI Client": {
                "status": "ok" if self.ai_client else "error",
                "details": self.config.get("api", {}).get("model", "Not configured")
            },
            "Security": {
                "status": "ok" if self.ignore_controller else "error",
                "details": f"Ignore file: {self.ignore_controller.has_ignore_file()}" if self.ignore_controller else "Not initialized"
            },
            "Tools": {
                "status": "ok" if all([self.file_ops, self.command_executor, self.file_searcher]) else "error",
                "details": "All tools loaded" if all([self.file_ops, self.command_executor, self.file_searcher]) else "Some tools missing"
            }
        }
        
        self.cli.display_status(status_info)
        
    async def _show_config(self):
        """Show current configuration"""
        # Redact sensitive information
        safe_config = self.config.copy()
        if "api" in safe_config and "key" in safe_config["api"]:
            key = safe_config["api"]["key"]
            safe_config["api"]["key"] = key[:10] + "..." + key[-4:] if len(key) > 14 else "***"
            
        config_text = json.dumps(safe_config, indent=2)
        self.cli.console.print(f"[bold]Current Configuration:[/bold]\n{config_text}")
        
    async def _cleanup(self):
        """Cleanup resources"""
        if self.ai_client:
            # AI client cleanup is handled by context manager
            pass
            
        logging.info("PyIDE shutdown complete")


async def main():
    """Main entry point"""
    try:
        app = PyIDE()
        await app.initialize()
        await app.run()
    except KeyboardInterrupt:
        print("\nShutdown requested by user")
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
