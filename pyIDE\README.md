# PyIDE - AI-Powered Python IDE

A Python implementation that replicates the AI interaction and file operations workflow from Cline, featuring secure file operations, real-time AI streaming, and comprehensive tool support.

## Features

- **AI Request-Response System**: OpenRouter API integration with streaming responses
- **Response Parsing Engine**: XML-based tool call extraction with state machine parser
- **File Operations**: Secure file operations with ignore patterns and user approval
- **Diff-Based Editing**: Side-by-side diff view with atomic operations
- **Tool Support**: Complete set of development tools (read, write, execute, search)

## Quick Start

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Configure API settings in `config.json`

3. Run the IDE:
```bash
python main.py
```

## Project Structure

```
pyIDE/
├── main.py                 # Main application entry point
├── config.json            # Configuration file
├── requirements.txt       # Python dependencies
├── core/
│   ├── __init__.py
│   ├── ai_client.py       # OpenRouter API client
│   ├── parser.py          # XML response parser
│   ├── tools/             # Tool implementations
│   │   ├── __init__.py
│   │   ├── file_ops.py    # File operation tools
│   │   ├── command.py     # Command execution
│   │   └── search.py      # File search tools
│   ├── security/          # Security and validation
│   │   ├── __init__.py
│   │   ├── ignore.py      # Ignore pattern handling
│   │   └── validator.py   # Path validation
│   └── ui/                # User interface
│       ├── __init__.py
│       ├── cli.py         # Command line interface
│       └── diff_view.py   # Diff viewer
├── examples/              # Example usage
│   └── demo.py
└── tests/                 # Test suite
    ├── __init__.py
    ├── test_parser.py
    ├── test_tools.py
    └── test_security.py
```

## Configuration

Edit `config.json` to configure API settings:

```json
{
    "api": {
        "provider": "openrouter",
        "key": "your-api-key",
        "model": "deepseek/deepseek-r1-0528:free",
        "base_url": "https://openrouter.ai/api/v1"
    },
    "security": {
        "max_file_size": 20971520,
        "workspace_only": true,
        "require_approval": true
    },
    "ui": {
        "interface": "cli",
        "show_diffs": true,
        "auto_approve": false
    }
}
```

## Usage Examples

### Basic AI Interaction
```python
from core.ai_client import AIClient
from core.parser import ResponseParser

client = AIClient()
parser = ResponseParser()

response = await client.stream_request("Create a hello world Python script")
for chunk in response:
    content_blocks = parser.parse_streaming(chunk)
    # Process content blocks...
```

### File Operations
```python
from core.tools.file_ops import FileOperations

file_ops = FileOperations()
content = await file_ops.read_file("example.py")
await file_ops.write_file("new_file.py", "print('Hello, World!')")
```

## Security Features

- **Ignore Patterns**: `.pyideignore` file support (gitignore syntax)
- **Workspace Boundaries**: Restrict operations to workspace directory
- **File Size Limits**: Configurable maximum file size (default 20MB)
- **User Approval**: Interactive approval for all file operations
- **Path Validation**: Prevent directory traversal attacks

## Tool Reference

### File Operations
- `read_file`: Read file contents with encoding detection
- `write_to_file`: Create or overwrite files with user approval
- `replace_in_file`: Make targeted edits using SEARCH/REPLACE blocks
- `list_files`: List directory contents with filtering
- `search_files`: Search files using regex patterns

### System Operations
- `execute_command`: Run terminal commands with output capture
- `ask_followup_question`: Interactive user queries

### Development Tools
- Real-time diff viewing
- Syntax highlighting
- Error detection and reporting
- Auto-formatting detection

## API Integration

The system uses OpenRouter API with the following features:
- Streaming response handling
- Automatic retry logic
- Error handling and recovery
- Token usage tracking
- Cost monitoring

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## License

MIT License - see LICENSE file for details
